<template>
  <div class="padding-bottom-30">
    <!-- 水站配送统计管理 begin ----------- -->
    <div>
      <div class="content-box"></div>
      <div class="cont-cent">
        <el-form :inline="true">
          <el-form-item>
            <el-input placeholder="输入水站名称/电话搜索" v-model="waterStationName" clearable style="width:200px">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-cascader v-model="areaFilter" placeholder="全部(区域)" :options="areaOptions" :props="cascaderProps"
              filterable clearable style="width: 200px;" @change="onAreaFilterChange">
            </el-cascader>
          </el-form-item>
          <el-form-item>
            <el-select v-model="storeId" filterable clearable placeholder="全部(商家)">
              <el-option label="全部(商家)" value=""></el-option>
              <el-option v-for="item in filteredStoreList" :key="item.storeId" :label="item.storeAme"
                :value="item.storeId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="ordersource" filterable clearable placeholder="全部(订单来源)">
              <el-option label="全部(订单来源)" value=""></el-option>
              <el-option v-for="item in ordersourcefilter" :key="item.key" :label="item.value" :value="item.key"
                :disabled="item.disabled" v-show="item.isShow">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="appkey" filterable clearable placeholder="全部(应用)">
              <el-option v-for="item in appkeylist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker @change="selectDataDate" v-model="dateTime" type="daterange" align="right" unlink-panels
              range-separator="至" start-placeholder="开始日期(创建时间)" end-placeholder="结束日期(创建时间)"
              :picker-options="pickerOptions" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-date-picker @change="selectDataDateFinish" v-model="dateTimeFinish" type="daterange" align="right"
              unlink-panels range-separator="至" start-placeholder="开始日期(送达时间)" end-placeholder="结束日期(送达时间)"
              :picker-options="pickerOptions" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select v-model="backFilter" placeholder="退款订单筛选" clearable>
              <el-option label="全部订单" value=""></el-option>
              <el-option label="退单订单" value="1"></el-option>
              <el-option label="正常订单" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="margin-left:20px;">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="clearSearch">清空筛选条件</el-button>
          </el-form-item>
        </el-form>
        <el-button type="success" icon="el-icon-download" size="mini" @click="exportExcel">导出数据</el-button>
      </div>
      <div class="royalty-cont">
        <el-table :data="tableData" :max-height="tableHeight + 50" border v-loading="fuckLoading10"
          element-loading-text="拼命加载中" :header-cell-style="{
            'text-align': 'center',
            background: '#EFF2F7',
            color: '#3D4C66',
          }" :cell-style="{
            'text-align': 'center',
            'font-size': '13px',
            color: '#333C48',
          }" empty-text="暂无数据" style="width:100%" size="small" stripe>
          <el-table-column label="水站名称" prop="waterStationName" min-width="120">
            <template slot-scope="scope">
              <div>{{ scope.row.waterStationName || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="水站电话" prop="waterStationPhone" min-width="120">
            <template slot-scope="scope">
              <div>{{ scope.row.waterStationPhone || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="总配送单数" prop="totalDeliveryCount" min-width="100">
            <template slot-scope="scope">
              <div style="color: #409EFF; font-weight: bold;">{{ scope.row.totalDeliveryCount || 0 }}单</div>
            </template>
          </el-table-column>
          <el-table-column label="已完成单数" prop="completedCount" min-width="100">
            <template slot-scope="scope">
              <div style="color: #67C23A;">{{ scope.row.completedCount || 0 }}单</div>
            </template>
          </el-table-column>
          <el-table-column label="配送中单数" prop="inProgressCount" min-width="100">
            <template slot-scope="scope">
              <div style="color: #E6A23C;">{{ scope.row.inProgressCount || 0 }}单</div>
            </template>
          </el-table-column>
          <el-table-column label="待配送单数" prop="pendingCount" min-width="100">
            <template slot-scope="scope">
              <div style="color: #F56C6C;">{{ scope.row.pendingCount || 0 }}单</div>
            </template>
          </el-table-column>
          <el-table-column label="退款单数" prop="refundCount" min-width="100">
            <template slot-scope="scope">
              <div style="color: #909399;">{{ scope.row.refundCount || 0 }}单</div>
            </template>
          </el-table-column>
          <el-table-column label="配送商品总数" prop="totalProductCount" min-width="120">
            <template slot-scope="scope">
              <div>{{ scope.row.totalProductCount || 0 }}件</div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="订单总金额" prop="totalOrderAmount" min-width="120">
            <template slot-scope="scope">
              <div style="color: #E6A23C; font-weight: bold;">￥{{ (scope.row.totalOrderAmount || 0) | SumFormat }}</div>
            </template>
          </el-table-column> -->
          <el-table-column label="完成率" prop="completionRate" min-width="80">
            <template slot-scope="scope">
              <div>{{ scope.row.completionRate || '0.00%' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="viewDeliveryOrders(scope.row)">
                查看配送订单
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: center;">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="page + 1"
            :page-sizes="currentPageSizes" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="pageTotal">
          </el-pagination>
        </div>

        <div class="total-box66">
          <div>合计</div>
          <div>
            <span>总配送单数：{{ totalStats.totalDeliveryCount }}单</span>
            <span>已完成：{{ totalStats.completedCount }}单</span>
            <span>配送中：{{ totalStats.inProgressCount }}单</span>
            <span>待配送：{{ totalStats.pendingCount }}单</span>
            <span>退款：{{ totalStats.refundCount }}单</span>
            <span>总配送商品：{{ totalStats.totalProductCount }}件</span>
            <!-- <span>总订单金额：￥{{ totalStats.totalOrderAmount | SumFormat }}</span> -->
          </div>
        </div>
      </div>
    </div>

    <!-- 配送订单查看弹窗 -->
    <el-dialog :title="deliveryOrderDialogTitle" :visible.sync="deliveryOrderVisible" width="90%" top="5vh"
      :before-close="closeDeliveryOrderDialog">
      <div v-loading="deliveryOrderLoading" element-loading-text="加载中...">
        <!-- 筛选条件 -->
        <el-form :inline="true" style="margin-bottom: 15px;">
          <el-form-item>
            <el-input placeholder="输入订单号/产品/手机号/联系方式/地址搜索" v-model="orderSearchText" clearable
              style="width:250px">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="orderDateRange" type="daterange" align="right" unlink-panels
              range-separator="至" start-placeholder="开始日期(下单时间)" end-placeholder="结束日期(下单时间)"
              :picker-options="pickerOptions" value-format="yyyy-MM-dd" clearable style="width: 240px;">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="orderFinishDateRange" type="daterange" align="right" unlink-panels
              range-separator="至" start-placeholder="开始日期(送达时间)" end-placeholder="结束日期(送达时间)"
              :picker-options="pickerOptions" value-format="yyyy-MM-dd" clearable style="width: 240px;">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select v-model="orderStatusFilter" placeholder="按订单状态筛选" clearable>
              <el-option v-for="(item, index) in orderStatusOptions" :key="index" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="orderBackFilter" placeholder="退款订单筛选" clearable>
              <el-option label="全部订单" value=""></el-option>
              <el-option label="退单订单" value="1"></el-option>
              <el-option label="正常订单" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchDeliveryOrders">查询</el-button>
            <el-button @click="clearOrderSearch">清空</el-button>
            <el-button type="success" icon="el-icon-download" @click="exportDeliveryOrders">导出订单</el-button>
          </el-form-item>
        </el-form>

        <!-- 订单列表 -->
        <el-table :data="deliveryOrderList" border stripe style="width: 100%;" size="mini" height="600"
          :header-cell-style="{ 'text-align': 'center', background: '#EFF2F7', color: '#3D4C66', 'font-size': '12px' }"
          :cell-style="{ 'text-align': 'center', 'font-size': '12px', color: '#333C48' }"
          empty-text="暂无配送订单数据">
          <el-table-column prop="mobile" label="订单来源" width="80">
            <template slot-scope="scope">
              <img v-if="scope.row.orderSourceImage" style="width: 40px;height: 40px;"
                :src="scope.row.orderSourceImage" alt="">
            </template>
          </el-table-column>
          <el-table-column prop="mobile" label="所属商家" min-width="120">
            <template slot-scope="scope">
              <div>{{ scope.row.storeName || '-' }}</div>
              <div v-if="scope.row.storeMobile">{{ scope.row.storeMobile || '-' }}</div>
              <div v-if="scope.row.storeProvinceName">
                {{ scope.row.storeProvinceName }}
                {{ scope.row.storeCityName ? ('-' + scope.row.storeCityName) : '' }}
                {{ scope.row.storeAreaName ? ('-' + scope.row.storeAreaName) : '' }}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="mobile" label="登录手机号" min-width="120"></el-table-column> -->
          <el-table-column prop="userName" label="联系人" min-width="100"></el-table-column>
          <el-table-column prop="userPhone" label="联系方式" min-width="120">
            <template slot-scope="scope">
              <div style="font-size: 11px;">{{ scope.row.userPhone }}</div>
              <el-button type="primary" size="mini"
                style="background-color: #1895fd; border: none; padding: 2px 6px; font-size: 10px; margin-top: 2px;"
                @click="refreshPhone(scope.row.orderId)" :loading="scope.row.refreshing">
                刷新电话
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="userAddress" label="下单地址" min-width="200"></el-table-column>
          <el-table-column prop="orderDate" label="下单日期" min-width="150"></el-table-column>
          <el-table-column prop="finishTime" label="送达日期" min-width="150"></el-table-column>
          <el-table-column prop="orderNumber" label="订单编码" width="150">
            <template slot-scope="scope">
              <div style="font-size: 11px;">{{ scope.row.orderNumber }}</div>
              <div @click="showOrderLog(scope.row.orderNumber)" class="order-log-btn" style="font-size: 10px;">
                操作记录
              </div>
            </template>
          </el-table-column>
          <el-table-column label="订单信息" min-width="150">
            <template slot-scope="scope">
              <div v-if="scope.row.groupShopList">
                <div v-for="(item, index) in scope.row.groupShopList" :key="index">
                  {{ item.groupName }} x {{ item.groupNumber }}
                </div>
              </div>
              <div v-if="scope.row.list && scope.row.list.length > 0">
                <div v-for="(item, index) in scope.row.list[0].orderShopDeatilList" :key="index">
                  {{ item.title }} x {{ item.shopNumber }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="orderTotalNumber" label="总件数" min-width="80"></el-table-column>
          <el-table-column prop="orderPrice" label="订单总金额" min-width="100">
            <template slot-scope="scope">￥{{ scope.row.orderPrice }}</template>
          </el-table-column>
          <el-table-column prop="deliveryName" label="送水员" min-width="120">
            <template slot-scope="scope">
              <div>{{ scope.row.deliveryName }}</div>
              <div v-if="scope.row.deliveryMobile">{{ scope.row.deliveryMobile }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="userContent" label="备注" min-width="120"></el-table-column>
          <el-table-column prop="newOrderState" label="状态" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.newOrderState }}</span>
              <span v-if="scope.row.orderReturnStete">({{ scope.row.orderReturnStete }})</span>
            </template>
          </el-table-column>
          <el-table-column prop="picurl" label="图片" width="120">
            <template slot-scope="scope">
              <div v-if="getPicUrlArray(scope.row.picurl).length > 0" class="pic-preview-btn-container">
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-picture-outline"
                  @click="previewImages(getPicUrlArray(scope.row.picurl), 0)"
                  class="pic-preview-btn">
                  查看图片({{ getPicUrlArray(scope.row.picurl).length }})
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: center;">
          <el-pagination @size-change="handleOrderSizeChange" @current-change="handleOrderCurrentChange"
            :current-page="orderPage + 1" :page-sizes="[10, 20, 50, 100]" :page-size="orderPageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="orderPageTotal">
          </el-pagination>
        </div>
      </div>
    </el-dialog>

    <!-- 订单操作记录弹窗 -->
    <el-dialog title="订单操作记录" :visible.sync="showOrderLogModal" width="50%" :before-close="closeOrderLogModal">
      <div v-loading="orderLogLoading" element-loading-text="加载中...">
        <div v-if="orderLogData && orderLogData.length > 0" class="order-log-content">
          <div class="log-item" v-for="(logItem, index) in orderLogData" :key="index">
            <div class="log-time">{{ logItem.createTime }}</div>
            <div class="log-content">{{ logItem.storeMsgModel }}</div>
            <div class="log-detail" v-if="logItem.content">{{ logItem.content }}</div>
          </div>
        </div>
        <div v-else-if="!orderLogLoading" class="no-log">
          <div class="no-log-text">暂无操作记录</div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览组件 -->
    <ImagePreviewUpload :visible.sync="imagePreviewVisible" :image-list="previewImageList"
      :current-index="currentImageIndex" @close="imagePreviewVisible = false" />
  </div>
</template>

<script>
import { ordersource } from "@/data/common"
import ImagePreviewUpload from "@/components/ImagePreviewUpload"

export default {
  props: {},
  data() {
    return {
      imgUri: this.$imgUri,
      fuckLoading10: false,

      // 筛选条件
      waterStationName: '',
      storeId: '',
      ordersource: '',
      appkey: '',
      startTime: '',
      endTime: '',
      dateTime: [],
      startTimeFinish: '',
      endTimeFinish: '',
      dateTimeFinish: [],
      backFilter: '', // 退款订单筛选

      // 区域筛选相关
      areaOptions: [], // 区域选项数据
      areaFilter: [], // 区域筛选字段
      cascaderProps: {
        value: "citiesid",
        label: "citie",
        children: "cities",
        emitPath: true,
        checkStrictly: true // 允许选择任意级别
      },
      allStoreList: [], // 所有商家数据（用于区域筛选）
      filteredStoreList: [], // 筛选后的商家数据
      adminStoreInfo: {}, // 管理员商家信息

      // 应用列表
      appkeylist: [
        { value: '', label: '全部(应用)' },
        { value: 'd794a292fc884a568a800d47ddaa5e01', label: '阿尔娃饮用水' },
        { value: '5c0f6c36b013486b8200ca0fa4a121e8', label: '好水送到家' },
        { value: '603690dcdf08490d9f134c29fe224248', label: '依美雪山' },
        { value: '06b1de86a7364508b3cc5d2e17bc6170', label: '飞蓝月泉' },
      ],

      // 订单来源筛选
      ordersourcefilter: ordersource,

      // 表格数据
      tableData: [],
      totalStats: {
        totalDeliveryCount: 0,
        completedCount: 0,
        inProgressCount: 0,
        pendingCount: 0,
        refundCount: 0,
        totalProductCount: 0,
        totalOrderAmount: 0
      },

      // 分页
      page: 0,
      pageSize: 10,
      pageTotal: 0,
      currentPageSizes: [10, 15, 20, 100],

      // 日期选择器配置
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },

      // 配送订单查看相关
      deliveryOrderVisible: false, // 配送订单弹窗显示状态
      deliveryOrderLoading: false, // 配送订单加载状态
      deliveryOrderDialogTitle: '', // 弹窗标题
      currentWaterStation: null, // 当前查看的水站信息
      deliveryOrderList: [], // 配送订单列表
      orderPage: 0, // 订单分页页码
      orderPageSize: 20, // 订单分页大小
      orderPageTotal: 0, // 订单总数
      orderSearchText: '', // 订单搜索文本
      orderStatusFilter: '', // 订单状态筛选
      orderBackFilter: '', // 退款订单筛选
      orderDateRange: [], // 订单日期范围（下单时间）
      orderFinishDateRange: [], // 订单完成日期范围（送达时间）

      // 订单状态选项（参考 orderMain.vue）
      orderStatusOptions: [
        {
          value: 2,
          label: "待发单"
        },
        {
          value: 3,
          label: "待接单"
        },
        {
          value: 4,
          label: "已发货"
        },
        {
          value: 10,
          label: "已完成"
        },
        {
          value: 8,
          label: "退款(申请中)"
        },
        {
          value: 11,
          label: "退款(已解决)"
        }
      ],

      // 图片预览相关
      imagePreviewVisible: false,
      previewImageList: [],
      currentImageIndex: 0,

      // 订单操作记录相关
      showOrderLogModal: false, // 是否显示操作记录弹窗
      orderLogData: null, // 操作记录数据列表
      orderLogLoading: false, // 操作记录加载状态
      currentOrderNum: '' // 当前查看的订单号
    }
  },

  computed: {
    tableHeight() {
      let height = Number(this.$store.getters.getGlobalHeight) - 250
      if (height >= 300) {
        return height
      } else {
        return 300
      }
    }
  },

  created() {
    this.adminStoreInfo = JSON.parse(this.Cookies.get("adminStoreInfo"))
    this.initDefaultDate(); // 初始化默认日期为当天
    this.getstore();
    this.getAreaOptions(); // 加载区域数据
    this.loadData()
  },

  methods: {
    // 初始化默认日期为当天
    initDefaultDate() {
      const today = new Date()
      const todayStr = today.getFullYear() + '-' +
        String(today.getMonth() + 1).padStart(2, '0') + '-' +
        String(today.getDate()).padStart(2, '0')

      // 设置创建时间默认为当天
      this.dateTime = [todayStr, todayStr]
      this.startTime = todayStr + " 00:00:00"
      this.endTime = todayStr + " 23:59:59"
    },

    // 获取商家列表
    getstore() {
      this.$post("/szmcstore/selectallstore", {
        storeId: this.adminStoreInfo.storeId
      }).then((res) => {
        if (res.code == 1) {
          this.allStoreList = res.data
          this.filteredStoreList = res.data // 初始化时显示所有商家
        } else {
          this.allStoreList = []
          this.filteredStoreList = []
        }
      }).catch(err => {
        console.log(err)
        this.$message.error("获取商家列表失败")
      })
    },

    // 获取区域选项数据
    getAreaOptions() {
      this.$post('/dpt/address/shanghai').then((res) => {
        if (res.code === 1) {
          this.areaOptions = res.data
        }
      }).catch((err) => {
        console.log('加载区域数据失败:', err)
      })
    },

    // 区域筛选变化处理
    onAreaFilterChange() {
      this.storeId = '' // 清空已选择的商家
      this.filterStoresByArea()
    },

    // 根据区域筛选商家
    filterStoresByArea() {
      if (!this.areaFilter || this.areaFilter.length === 0) {
        // 没有选择区域时，显示所有商家
        this.filteredStoreList = this.allStoreList
        return
      }

      const [provinceId, cityId, areaId] = this.areaFilter

      this.filteredStoreList = this.allStoreList.filter(store => {
        // 根据选择的区域级别进行筛选
        if (areaId) {
          // 选择了区/县，精确匹配区域
          return store.storeArea === areaId.toString()
        } else if (cityId) {
          // 选择了市，匹配城市
          return store.storeCity === cityId.toString()
        } else if (provinceId) {
          // 选择了省，匹配省份
          return store.storeProvince === provinceId.toString()
        }
        return true
      })
    },

    // 日期选择处理
    selectDataDate(val) {
      if (val && val.length === 2) {
        this.startTime = val[0] + " 00:00:00";
        this.endTime = val[1] + " 23:59:59";
      } else {
        this.startTime = ""
        this.endTime = ""
      }
    },

    selectDataDateFinish(val) {
      if (val && val.length === 2) {
        this.startTimeFinish = val[0] + " 00:00:00";
        this.endTimeFinish = val[1] + " 23:59:59";
      } else {
        this.startTimeFinish = ""
        this.endTimeFinish = ""
      }
    },

    search() {
      this.page = 0;
      this.loadData()
    },

    clearSearch() {
      this.page = 0;
      this.waterStationName = ""
      this.storeId = ""
      this.ordersource = ""
      this.appkey = ""
      this.startTime = ""
      this.endTime = ""
      this.dateTime = []
      this.startTimeFinish = ""
      this.endTimeFinish = ""
      this.dateTimeFinish = []
      this.backFilter = ""
      this.areaFilter = [] // 清除区域筛选
      this.filteredStoreList = this.allStoreList // 重置商家列表
      this.loadData()
    },

    loadData() {
      let that = this

      // 构建查询参数，添加异常处理
      let params = {
        waterStationId: -1, // 查询所有水站
        startTime: this.startTime || "",
        endTime: this.endTime || "",
        storeId: this.storeId || "",
        type: 1, // 自定义时间类型
        orderSource: this.ordersource || "",
        includeRefund: this.backFilter === "1" ? 1 : (this.backFilter === "0" ? 0 : null),
        // 添加额外的筛选参数
        waterStationName: this.waterStationName || "",
        appkey: this.appkey || "",
        startTimeFinish: this.startTimeFinish || "",
        endTimeFinish: this.endTimeFinish || "",
        // 分页参数
        page: this.page,
        pageSize: this.pageSize
      }

      // 显示加载状态
      this.fuckLoading10 = true

      that.$post("/szmb/szmsendmembercontroller/selectWaterStationDeliveryStatistics", params)
        .then(res => {
          this.fuckLoading10 = false
          if (res && res.code === 1) {
            that.tableData = (res.data && res.data.list) ? res.data.list : []
            that.pageTotal = (res.data && res.data.totalCount) ? res.data.totalCount : 0
            that.calculateTotalStats()
          } else {
            that.tableData = []
            that.pageTotal = 0
            that.totalStats = {
              totalDeliveryCount: 0,
              completedCount: 0,
              inProgressCount: 0,
              pendingCount: 0,
              refundCount: 0,
              totalProductCount: 0,
              totalOrderAmount: 0
            }
            if (res && res.msg) {
              that.$message.error(res.msg)
            } else {
              that.$message.error("获取数据失败")
            }
          }
        })
        .catch(err => {
          this.fuckLoading10 = false
          console.error("水站配送统计查询异常:", err)
          that.tableData = []
          that.pageTotal = 0
          that.totalStats = {
            totalDeliveryCount: 0,
            completedCount: 0,
            inProgressCount: 0,
            pendingCount: 0,
            refundCount: 0,
            totalProductCount: 0,
            totalOrderAmount: 0
          }
          that.$message.error("网络请求失败，请稍后重试")
        })
    },

    calculateTotalStats() {
      try {
        this.totalStats = {
          totalDeliveryCount: this.tableData.reduce((sum, item) => sum + (item.totalDeliveryCount || 0), 0),
          completedCount: this.tableData.reduce((sum, item) => sum + (item.completedCount || 0), 0),
          inProgressCount: this.tableData.reduce((sum, item) => sum + (item.inProgressCount || 0), 0),
          pendingCount: this.tableData.reduce((sum, item) => sum + (item.pendingCount || 0), 0),
          refundCount: this.tableData.reduce((sum, item) => sum + (item.refundCount || 0), 0),
          totalProductCount: this.tableData.reduce((sum, item) => sum + (item.totalProductCount || 0), 0),
          totalOrderAmount: this.tableData.reduce((sum, item) => sum + (item.totalOrderAmount || 0), 0)
        }
      } catch (error) {
        console.error("计算统计数据异常:", error)
        this.totalStats = {
          totalDeliveryCount: 0,
          completedCount: 0,
          inProgressCount: 0,
          pendingCount: 0,
          refundCount: 0,
          totalProductCount: 0,
          totalOrderAmount: 0
        }
      }
    },

    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.loadData()
    },

    handleCurrentChange(val) {
      this.page = val - 1
      this.loadData()
    },

    exportExcel() {
      try {
        // 构建导出参数
        var params = [
          "page=1",
          "pageSize=10000",
          "waterStationName=" + encodeURIComponent(this.waterStationName || ''),
          "storeId=" + encodeURIComponent(this.storeId || ''),
          "ordersource=" + encodeURIComponent(this.ordersource || ''),
          "appkey=" + encodeURIComponent(this.appkey || ''),
          "startTime=" + encodeURIComponent(this.startTime || ''),
          "endTime=" + encodeURIComponent(this.endTime || ''),
          "startTimeFinish=" + encodeURIComponent(this.startTimeFinish || ''),
          "endTimeFinish=" + encodeURIComponent(this.endTimeFinish || ''),
          "backFilter=" + encodeURIComponent(this.backFilter || '')
        ].join("&")

        var url = this.$axios.adornUrl("/szmb/szmsendmembercontroller/selectWaterStationDeliveryStatisticsExport?" + params)
        window.open(url)
      } catch (error) {
        console.error("导出异常:", error)
        this.$message.error("导出失败，请稍后重试")
      }
    },

    // 查看配送订单
    viewDeliveryOrders(waterStation) {
      this.currentWaterStation = waterStation
      this.deliveryOrderDialogTitle = `${waterStation.waterStationName} - 配送订单列表`
      this.deliveryOrderVisible = true
      this.orderPage = 0
      this.orderSearchText = ''
      this.orderStatusFilter = ''
      this.orderBackFilter = ''
      this.orderDateRange = []
      this.orderFinishDateRange = []
      this.loadDeliveryOrders()
    },

    // 加载配送订单数据
    loadDeliveryOrders() {
      if (!this.currentWaterStation) return

      this.deliveryOrderLoading = true

      // 处理日期范围
      let orderStartTime = ''
      let orderEndTime = ''
      let orderFinishStartTime = ''
      let orderFinishEndTime = ''

      if (this.orderDateRange && this.orderDateRange.length === 2) {
        orderStartTime = this.orderDateRange[0]
        orderEndTime = this.orderDateRange[1]
      }

      if (this.orderFinishDateRange && this.orderFinishDateRange.length === 2) {
        orderFinishStartTime = this.orderFinishDateRange[0]
        orderFinishEndTime = this.orderFinishDateRange[1]
      }

      // 构建查询参数，参考 orderMainall 的实现
      let params = {
        storeId: this.currentWaterStation.waterStationId, // 使用水站ID作为商家ID筛选
        username: this.orderSearchText || '',
        userContent: '',
        startTime: orderStartTime,
        endTime: orderEndTime,
        startTimeFinish: orderFinishStartTime,
        endTimeFinish: orderFinishEndTime,
        appkey: this.appkey || '',
        ordersource: this.ordersource || '',
        addressId: '',
        index: this.orderPage,
        pageSize: this.orderPageSize,
        orderStatus: this.orderStatusFilter || '',
        back: this.orderBackFilter || ''
      }

      // 使用与 orderMainall 相同的接口
      this.$post('/szmcordermaincontroller/findallorderall', params)
        .then(res => {
          this.deliveryOrderLoading = false
          if (res.code === 1) {
            this.deliveryOrderList = res.data.list || []
            this.orderPageTotal = res.data.count || 0
          } else {
            this.deliveryOrderList = []
            this.orderPageTotal = 0
            this.$message.error(res.msg || '获取配送订单失败')
          }
        })
        .catch(err => {
          this.deliveryOrderLoading = false
          console.error('获取配送订单异常:', err)
          this.deliveryOrderList = []
          this.orderPageTotal = 0
          this.$message.error('网络请求失败，请稍后重试')
        })
    },

    // 搜索配送订单
    searchDeliveryOrders() {
      this.orderPage = 0
      this.loadDeliveryOrders()
    },

    // 清空订单搜索条件
    clearOrderSearch() {
      this.orderSearchText = ''
      this.orderStatusFilter = ''
      this.orderBackFilter = ''
      this.orderDateRange = []
      this.orderFinishDateRange = []
      this.orderPage = 0
      this.loadDeliveryOrders()
    },

    // 关闭配送订单弹窗
    closeDeliveryOrderDialog() {
      this.deliveryOrderVisible = false
      this.currentWaterStation = null
      this.deliveryOrderList = []
      this.orderPage = 0
      this.orderPageTotal = 0
      this.orderSearchText = ''
      this.orderStatusFilter = ''
      this.orderBackFilter = ''
      this.orderDateRange = []
      this.orderFinishDateRange = []
    },

    // 订单分页处理
    handleOrderSizeChange(val) {
      this.orderPageSize = val
      this.orderPage = 0
      this.loadDeliveryOrders()
    },

    handleOrderCurrentChange(val) {
      this.orderPage = val - 1
      this.loadDeliveryOrders()
    },

    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        0: '待接单',
        1: '已接单',
        2: '配送中',
        3: '配送中',
        4: '配送中',
        5: '已完成',
        6: '已退款',
        8: '已退款'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取订单状态标签类型
    getOrderStatusType(status) {
      const typeMap = {
        0: 'info',
        1: 'warning',
        2: 'primary',
        3: 'primary',
        4: 'primary',
        5: 'success',
        6: 'danger',
        8: 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 导出配送订单
    exportDeliveryOrders() {
      if (!this.currentWaterStation) {
        this.$message.error('请先选择要导出的水站')
        return
      }

      try {
        // 处理日期范围
        let orderStartTime = ''
        let orderEndTime = ''
        let orderFinishStartTime = ''
        let orderFinishEndTime = ''

        if (this.orderDateRange && this.orderDateRange.length === 2) {
          orderStartTime = this.orderDateRange[0]
          orderEndTime = this.orderDateRange[1]
        }

        if (this.orderFinishDateRange && this.orderFinishDateRange.length === 2) {
          orderFinishStartTime = this.orderFinishDateRange[0]
          orderFinishEndTime = this.orderFinishDateRange[1]
        }

        // 构建导出参数，参考 orderMainall 的实现
        var params = [
          "page=1",
          "pageSize=10000",
          "username=" + encodeURIComponent(this.orderSearchText || ''),
          "userContent=",
          "startTime=" + encodeURIComponent(orderStartTime),
          "endTime=" + encodeURIComponent(orderEndTime),
          "startTimeFinish=" + encodeURIComponent(orderFinishStartTime),
          "endTimeFinish=" + encodeURIComponent(orderFinishEndTime),
          "appkey=" + encodeURIComponent(this.appkey || ''),
          "ordersource=" + encodeURIComponent(this.ordersource || ''),
          "orderStatus=" + encodeURIComponent(this.orderStatusFilter || ''),
          "storeId=" + encodeURIComponent(this.currentWaterStation.waterStationId || ''),
          "timeoutFilter=",
          "timeoutType=",
          "back=" + encodeURIComponent(this.orderBackFilter || ''),
          "addressId="
        ]

        var url = this.$axios.adornUrl("/szmcordermaincontroller/exportplatformorder?" + params.join('&'))
        window.open(url)
      } catch (error) {
        console.error("导出异常:", error)
        this.$message.error("导出失败，请稍后重试")
      }
    },

    // 刷新电话功能
    refreshPhone(orderId) {
      // 设置当前订单的刷新状态
      const order = this.deliveryOrderList.find(item => item.orderId === orderId)
      if (order) {
        this.$set(order, 'refreshing', true)
      }

      this.$post("/szmb/szmborder/refreshphone", { orderId: orderId }).then((res) => {
        // 清除刷新状态
        if (order) {
          this.$set(order, 'refreshing', false)
        }

        if (res.code == 1) {
          this.$message.success("刷新成功")
          this.loadDeliveryOrders() // 刷新订单列表
        } else {
          this.$message.error("刷新失败: " + (res.msg || res.data || '未知错误'))
        }
      }).catch((err) => {
        // 清除刷新状态
        if (order) {
          this.$set(order, 'refreshing', false)
        }
        console.error("刷新电话失败:", err)
        this.$message.error("刷新失败，请稍后重试")
      })
    },

    // 显示订单操作记录
    showOrderLog(orderNum) {
      if (!orderNum) {
        this.$message.error('订单号不能为空')
        return
      }

      this.currentOrderNum = orderNum
      this.showOrderLogModal = true
      this.getOrderLog(orderNum)
    },

    // 获取订单操作记录
    getOrderLog(orderNum) {
      this.orderLogLoading = true
      this.orderLogData = null

      this.$post('/szmb/msg/selectmsgbyordernum', {
        orderNum: orderNum
      }).then((res) => {
        this.orderLogLoading = false
        if (res.code == 1) {
          // 如果返回的是单个对象，转换为数组
          if (res.data && !Array.isArray(res.data)) {
            this.orderLogData = [res.data]
          } else {
            this.orderLogData = res.data || []
          }
        } else {
          this.orderLogData = []
          this.$message.error('获取操作记录失败: ' + (res.msg || '未知错误'))
        }
      }).catch((err) => {
        this.orderLogLoading = false
        console.error('获取操作记录失败:', err)
        this.orderLogData = null
        this.$message.error('网络请求失败')
      })
    },

    // 关闭订单操作记录弹窗
    closeOrderLogModal() {
      this.showOrderLogModal = false
      this.orderLogData = null
      this.orderLogLoading = false
      this.currentOrderNum = ''
    },

    // 处理picurl字符串，分割成数组
    getPicUrlArray(picurl) {
      if (!picurl || typeof picurl !== 'string') {
        return []
      }
      return picurl.split(',').filter(url => url.trim() !== '')
    },

    // 预览图片
    previewImages(imageList, currentIndex = 0) {
      this.previewImageList = imageList
      this.currentImageIndex = currentIndex
      this.imagePreviewVisible = true
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.style.display = 'none'
    }
  },

  components: {
    ImagePreviewUpload
  }
}
</script>

<style scoped>
.padding-bottom-30 {
  padding-bottom: 30px;
}

.content-box {
  height: 20px;
}

.cont-cent {
  margin-bottom: 20px;
}

.royalty-cont {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.total-box66 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.total-box66>div:last-child span {
  margin-right: 30px;
  font-weight: bold;
}

/* 订单操作记录按钮样式 */
.order-log-btn {
  color: #67c23a;
  cursor: pointer;
  transition: all 0.3s;
  padding: 2px 0;
  font-size: 10px;
}

.order-log-btn:hover {
  color: #5daf34;
  text-decoration: underline;
}

/* 订单操作记录弹窗样式 */
.order-log-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px 0;
}

.log-item {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
}

.log-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.log-content {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.log-detail {
  font-size: 12px;
  color: #666;
  border-top: 1px solid #e9ecef;
  padding-top: 10px;
  margin-bottom: 15px;
}

.no-log {
  text-align: center;
  padding: 40px;
  color: #999;
}

.no-log-text {
  font-size: 14px;
}

/* 图片预览按钮样式 */
.pic-preview-btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pic-preview-btn {
  font-size: 12px;
  padding: 4px 8px;
  color: #409eff;
}

.pic-preview-btn:hover {
  color: #66b1ff;
}

.pic-preview-btn .el-icon-picture-outline {
  margin-right: 4px;
}
</style>
