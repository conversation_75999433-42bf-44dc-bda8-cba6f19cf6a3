package com.example.waterstationbuyproducer.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单来源关联表
 * 用于关联ordersource、skuId和product_id
 */
public class OrderSourceConnect implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 唯一标识
     */
    private String unionCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 关联产品id
     */
    private Long productNewId;

    /**
     * 关联订单来源id
     */
    private Long orderSourceId;

    /**
     * 商品件数
     */
    private Integer productQuantity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 产品名称（非数据库字段，用于查询结果展示）
     */
    private String productName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUnionCode() {
        return unionCode;
    }

    public void setUnionCode(String unionCode) {
        this.unionCode = unionCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getProductNewId() {
        return productNewId;
    }

    public void setProductNewId(Long productNewId) {
        this.productNewId = productNewId;
    }

    public Long getOrderSourceId() {
        return orderSourceId;
    }

    public void setOrderSourceId(Long orderSourceId) {
        this.orderSourceId = orderSourceId;
    }

    public Integer getProductQuantity() {
        return productQuantity;
    }

    public void setProductQuantity(Integer productQuantity) {
        this.productQuantity = productQuantity;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
